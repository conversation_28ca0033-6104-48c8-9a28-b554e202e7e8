//
//  MultiAgentPerformanceTest.swift
//  travel with
//
//  Created by Augment Agent on 2025/8/5.
//  多智能体系统性能测试 - 验证并行处理优化效果
//

import Foundation
import SwiftUI

/// 多智能体系统性能测试类
@MainActor
class MultiAgentPerformanceTest: ObservableObject {
    
    @Published var testResults: [TestResult] = []
    @Published var isRunning = false
    
    private let multiAgentService: MultiAgentIntegrationService
    
    init(multiAgentService: MultiAgentIntegrationService) {
        self.multiAgentService = multiAgentService
    }
    
    /// 运行性能测试
    func runPerformanceTest() async {
        isRunning = true
        testResults.removeAll()
        
        let testCases = [
            "你好，今天天气怎么样？",
            "我想去旅行，有什么推荐的地方吗？",
            "我有点喜欢一个女生已经一年多了然后但是他一直都没有接受我我现在有点烦恼你觉得喜欢是默默的喜欢吗他应该不是",
            "帮我规划一下明天的行程",
            "我现在心情不太好，能陪我聊聊吗？"
        ]
        
        print("🧪 开始多智能体性能测试...")
        
        for (index, testCase) in testCases.enumerated() {
            print("🧪 测试用例 \(index + 1)/\(testCases.count): \(testCase.prefix(20))...")
            
            let startTime = Date()
            let response = await multiAgentService.processTextMessage(testCase)
            let endTime = Date()
            
            let processingTime = endTime.timeIntervalSince(startTime)
            
            let result = TestResult(
                testCase: testCase,
                processingTime: processingTime,
                responseLength: response.content.count,
                timestamp: Date()
            )
            
            testResults.append(result)
            
            print("✅ 测试用例 \(index + 1) 完成，耗时: \(String(format: "%.2f", processingTime))秒")
            
            // 等待1秒再进行下一个测试，避免系统过载
            try? await Task.sleep(nanoseconds: 1_000_000_000)
        }
        
        isRunning = false
        printTestSummary()
    }
    
    /// 打印测试总结
    private func printTestSummary() {
        guard !testResults.isEmpty else { return }
        
        let totalTime = testResults.reduce(0) { $0 + $1.processingTime }
        let averageTime = totalTime / Double(testResults.count)
        let minTime = testResults.min { $0.processingTime < $1.processingTime }?.processingTime ?? 0
        let maxTime = testResults.max { $0.processingTime < $1.processingTime }?.processingTime ?? 0
        
        print("\n📊 多智能体性能测试总结:")
        print("📝 测试用例数量: \(testResults.count)")
        print("⏱️ 平均处理时间: \(String(format: "%.2f", averageTime))秒")
        print("🚀 最快处理时间: \(String(format: "%.2f", minTime))秒")
        print("🐌 最慢处理时间: \(String(format: "%.2f", maxTime))秒")
        print("📊 总处理时间: \(String(format: "%.2f", totalTime))秒")
        
        // 性能评估
        if averageTime <= 4.0 {
            print("🎉 性能优秀！平均响应时间在4秒以内")
        } else if averageTime <= 6.0 {
            print("✅ 性能良好！平均响应时间在6秒以内")
        } else {
            print("⚠️ 性能需要优化，平均响应时间超过6秒")
        }
    }
}

/// 测试结果数据模型
struct TestResult: Identifiable {
    let id = UUID()
    let testCase: String
    let processingTime: TimeInterval
    let responseLength: Int
    let timestamp: Date
    
    var formattedTime: String {
        String(format: "%.2f秒", processingTime)
    }
    
    var performanceLevel: PerformanceLevel {
        if processingTime <= 3.0 {
            return .excellent
        } else if processingTime <= 5.0 {
            return .good
        } else if processingTime <= 7.0 {
            return .fair
        } else {
            return .poor
        }
    }
}

/// 性能等级
enum PerformanceLevel: String, CaseIterable {
    case excellent = "优秀"
    case good = "良好"
    case fair = "一般"
    case poor = "较差"
    
    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        }
    }
    
    var emoji: String {
        switch self {
        case .excellent: return "🚀"
        case .good: return "✅"
        case .fair: return "⚠️"
        case .poor: return "🐌"
        }
    }
}

/// 性能测试界面
struct MultiAgentPerformanceTestView: View {
    @StateObject private var performanceTest: MultiAgentPerformanceTest
    
    init(multiAgentService: MultiAgentIntegrationService) {
        self._performanceTest = StateObject(wrappedValue: MultiAgentPerformanceTest(multiAgentService: multiAgentService))
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 测试控制区域
                VStack(spacing: 15) {
                    Text("多智能体性能测试")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("测试并行处理优化效果")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Button(action: {
                        Task {
                            await performanceTest.runPerformanceTest()
                        }
                    }) {
                        HStack {
                            if performanceTest.isRunning {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("测试进行中...")
                            } else {
                                Image(systemName: "play.circle.fill")
                                Text("开始性能测试")
                            }
                        }
                        .foregroundColor(.white)
                        .padding()
                        .background(performanceTest.isRunning ? Color.gray : Color.blue)
                        .cornerRadius(10)
                    }
                    .disabled(performanceTest.isRunning)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(15)
                
                // 测试结果列表
                if !performanceTest.testResults.isEmpty {
                    List(performanceTest.testResults) { result in
                        TestResultRow(result: result)
                    }
                    .listStyle(PlainListStyle())
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("性能测试")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

/// 测试结果行视图
struct TestResultRow: View {
    let result: TestResult
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(result.performanceLevel.emoji)
                Text(result.testCase.prefix(30) + (result.testCase.count > 30 ? "..." : ""))
                    .font(.subheadline)
                    .lineLimit(1)
                Spacer()
                Text(result.formattedTime)
                    .font(.caption)
                    .fontWeight(.semibold)
                    .foregroundColor(result.performanceLevel.color)
            }
            
            HStack {
                Text("响应长度: \(result.responseLength)字符")
                    .font(.caption2)
                    .foregroundColor(.secondary)
                Spacer()
                Text(result.performanceLevel.rawValue)
                    .font(.caption2)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(result.performanceLevel.color.opacity(0.2))
                    .foregroundColor(result.performanceLevel.color)
                    .cornerRadius(4)
            }
        }
        .padding(.vertical, 4)
    }
}
