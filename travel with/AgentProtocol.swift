//
//  AgentProtocol.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - 智能体基础协议
/// 定义所有智能体必须实现的基础接口
/// 确保智能体之间的一致性和可互操作性
protocol Agent {
    /// 智能体唯一标识符
    var identifier: AgentIdentifier { get }
    
    /// 智能体名称
    var name: String { get }
    
    /// 智能体描述
    var description: String { get }
    
    /// 共享状态中心引用
    var sharedState: SharedStateHub { get }
    
    /// 智能体是否就绪
    var isReady: Bool { get }
    
    /// 处理输入并返回输出
    /// - Parameter input: 智能体输入数据
    /// - Returns: 智能体输出结果
    func process(_ input: AgentInput) async -> AgentOutput
    
    /// 判断是否能处理特定意图
    /// - Parameter intent: 用户意图
    /// - Returns: 是否能处理该意图
    func canHandle(_ intent: UserIntent) -> Bool
    
    /// 获取处理该意图的优先级（0.0-1.0，越高越优先）
    /// - Parameter intent: 用户意图
    /// - Returns: 优先级分数
    func getPriority(for intent: UserIntent) -> Double
    
    /// 初始化智能体
    func initialize() async
    
    /// 获取智能体状态摘要
    func getStatusSummary() -> String
}

// MARK: - 智能体标识符扩展
/// 扩展现有的AgentIdentifier枚举，添加新的智能体类型
extension AgentIdentifier {
    /// 获取智能体显示名称
    var displayName: String {
        switch self {
        case .taskAssignment: return "任务分配智能体"
        case .thinkCommunication: return "沟通智能体"
        case .see: return "多模态感知智能体"
        }
    }

    /// 获取新的智能体标识符映射
    static var deepThinking: AgentIdentifier { return .thinkCommunication }
    static var multimodal: AgentIdentifier { return .see }
}

// MARK: - 智能体输入输出模型

/// 智能体输入数据
struct AgentInput {
    let userMessage: String                    // 用户消息
    let messageType: MessageType               // 消息类型
    let imageData: Data?                       // 图像数据（如果有）
    let context: ConversationContext           // 对话上下文
    let timestamp: Date                        // 时间戳
    let metadata: [String: Any]                // 额外元数据
    
    init(userMessage: String, 
         messageType: MessageType = .text, 
         imageData: Data? = nil, 
         context: ConversationContext, 
         metadata: [String: Any] = [:]) {
        self.userMessage = userMessage
        self.messageType = messageType
        self.imageData = imageData
        self.context = context
        self.timestamp = Date()
        self.metadata = metadata
    }
}

/// 智能体输出结果
struct AgentOutput {
    let content: String                        // 输出内容
    let confidence: Double                     // 置信度 (0.0-1.0)
    let processingTime: TimeInterval           // 处理时间
    let emotionRecommendation: EmotionState?   // 推荐的情感状态
    let ttsEmotion: CanCanEmotion?            // 推荐的TTS情感音色
    let metadata: [String: Any]                // 额外元数据
    let agentId: AgentIdentifier               // 生成此输出的智能体ID
    
    init(content: String, 
         confidence: Double = 1.0, 
         processingTime: TimeInterval = 0, 
         emotionRecommendation: EmotionState? = nil, 
         ttsEmotion: CanCanEmotion? = nil, 
         metadata: [String: Any] = [:], 
         agentId: AgentIdentifier) {
        self.content = content
        self.confidence = max(0.0, min(1.0, confidence))
        self.processingTime = processingTime
        self.emotionRecommendation = emotionRecommendation
        self.ttsEmotion = ttsEmotion
        self.metadata = metadata
        self.agentId = agentId
    }
}

/// 消息类型
enum MessageType: String {
    case text = "text"           // 文本消息
    case image = "image"         // 图像消息
    case emoji = "emoji"         // 表情消息
    case voice = "voice"         // 语音消息
    case system = "system"       // 系统消息
}

// MARK: - 智能体执行计划

/// 智能体执行计划
struct AgentExecutionPlan {
    let primaryAgent: AgentIdentifier          // 主要智能体
    let supportingAgents: [AgentIdentifier]    // 支持智能体
    let executionOrder: ExecutionOrder         // 执行顺序
    let expectedProcessingTime: TimeInterval   // 预期处理时间
    let priority: Double                       // 优先级
    
    init(primaryAgent: AgentIdentifier, 
         supportingAgents: [AgentIdentifier] = [], 
         executionOrder: ExecutionOrder = .sequential, 
         expectedProcessingTime: TimeInterval = 5.0, 
         priority: Double = 0.5) {
        self.primaryAgent = primaryAgent
        self.supportingAgents = supportingAgents
        self.executionOrder = executionOrder
        self.expectedProcessingTime = expectedProcessingTime
        self.priority = priority
    }
}

/// 执行顺序
enum ExecutionOrder {
    case sequential     // 顺序执行
    case parallel       // 并行执行
    case conditional    // 条件执行
}

// MARK: - 智能体执行结果

/// 智能体执行结果
struct AgentExecutionResult {
    let outputs: [AgentOutput]                 // 所有智能体的输出
    let finalResponse: String                  // 最终整合的响应
    let totalProcessingTime: TimeInterval      // 总处理时间
    let executedAgents: [AgentIdentifier]      // 实际执行的智能体
    let errors: [AgentError]                   // 执行过程中的错误
    let metadata: [String: Any]                // 执行元数据
    
    init(outputs: [AgentOutput], 
         finalResponse: String, 
         totalProcessingTime: TimeInterval, 
         executedAgents: [AgentIdentifier], 
         errors: [AgentError] = [], 
         metadata: [String: Any] = [:]) {
        self.outputs = outputs
        self.finalResponse = finalResponse
        self.totalProcessingTime = totalProcessingTime
        self.executedAgents = executedAgents
        self.errors = errors
        self.metadata = metadata
    }
}

// MARK: - 错误处理

/// 智能体错误
struct AgentError: Error {
    let agentId: AgentIdentifier
    let errorType: AgentErrorType
    let message: String
    let timestamp: Date
    let underlyingError: Error?
    
    init(agentId: AgentIdentifier, 
         errorType: AgentErrorType, 
         message: String, 
         underlyingError: Error? = nil) {
        self.agentId = agentId
        self.errorType = errorType
        self.message = message
        self.timestamp = Date()
        self.underlyingError = underlyingError
    }
}

/// 智能体错误类型
enum AgentErrorType {
    case initialization     // 初始化错误
    case processing        // 处理错误
    case apiCall          // API调用错误
    case timeout          // 超时错误
    case invalidInput     // 无效输入
    case systemError      // 系统错误
}

// MARK: - TTS情感音色引用
// 注意：CanCanEmotion枚举已在TTSService.swift中定义，这里不重复定义
