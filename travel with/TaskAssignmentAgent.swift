//
//  TaskAssignmentAgent.swift
//  travel with
//
//  Created by Multi-Agent System on 2025/8/2.
//

import Foundation

// MARK: - 任务分配智能体
/// 负责分析用户意图，决定调用哪些下游智能体
/// 这是多智能体系统的核心调度组件
@MainActor
class TaskAssignmentAgent: BaseAgent {
    
    // MARK: - 初始化
    
    init(sharedState: SharedStateHub, scheduler: AgentScheduler?) {
        super.init(
            identifier: AgentIdentifier.taskAssignment,
            name: "任务分配智能体",
            description: "分析用户意图，决定调用哪些智能体进行协同处理",
            sharedState: sharedState,
            scheduler: scheduler
        )
    }
    
    // MARK: - 重写基类方法
    
    override func performSpecificProcessing(_ input: AgentInput) async -> ProcessingResult {
        print("🎯 任务分配智能体开始分析用户意图...")

        // 构建JSON格式的任务分配提示词
        let systemPrompt = generateTaskAssignmentJSONPrompt()

        // 准备用户消息，包含时间和上下文信息
        let userMessage = prepareUserMessageWithTimeContext(input)

        // 调用API进行任务分配分析
        let jsonResponse = await callAPI(systemPrompt: systemPrompt, userMessage: userMessage)

        print("📋 任务分配JSON结果: \(jsonResponse)")

        // 解析JSON响应
        let taskDecision = parseTaskDecisionJSON(jsonResponse)

        // 生成处理结果
        return ProcessingResult(
            content: jsonResponse,
            confidence: taskDecision?.confidence ?? 0.8,
            metadata: [
                "task_decision_json": jsonResponse,
                "parsed_decision": taskDecision != nil ? "success" : "failed"
            ]
        )
    }
    
    override func getHandleableIntents() -> [UserIntent] {
        // 任务分配智能体可以处理所有类型的意图分析
        return [.simpleChat, .deepChat, .imageQuery, .emotionalSupport, .travelPlanning, .unknown]
    }
    
    override func getBasePriority() -> Double {
        return 1.0 // 任务分配智能体具有最高优先级
    }
    
    // MARK: - 意图分析方法
    
    /// 生成JSON格式的任务分配提示词
    private func generateTaskAssignmentJSONPrompt() -> String {
        return """
        你是任务分配智能体，负责分析用户输入并返回JSON格式的任务分配决策。

        **重要：你只能返回JSON格式的结果，不能包含任何其他内容！**

        JSON格式要求：
        {
            "chat_text": "传给沟通智能体的文本内容",
            "confidence": 0.0-1.0,
            "reasoning": "简要分析理由"
        }

        字段说明：
        - chat_text: 直接传用户的原始输入文本，不要修改或总结
        - confidence: 决策的置信度（通常为0.9-1.0）
        - reasoning: 简要说明为什么这样分配任务

        请严格按照JSON格式返回，不要包含任何解释或其他内容。
        """
    }
    
    /// 准备包含上下文的用户消息
    private func prepareUserMessageWithContext(_ input: AgentInput) -> String {
        var contextInfo = ""
        
        // 添加消息类型信息
        switch input.messageType {
        case .image:
            contextInfo += "用户发送了一张图片。"
        case .voice:
            contextInfo += "用户发送了语音消息。"
        case .emoji:
            contextInfo += "用户发送了表情符号。"
        default:
            break
        }
        
        // 添加对话历史上下文
        let recentMessages = input.context.conversationHistory.suffix(3)
        if !recentMessages.isEmpty {
            contextInfo += "\n\n最近的对话上下文：\n"
            for message in recentMessages {
                contextInfo += "用户：\(message.userMessage)\n"
                if let aiResponse = message.aiResponse {
                    contextInfo += "AI：\(aiResponse)\n"
                }
            }
        }
        
        // 添加当前AI状态
        let aiLifeStatus = sharedState.getCurrentLifeStatus()
        contextInfo += "\n\nAI当前状态：\n\(aiLifeStatus)"
        
        return """
        \(contextInfo)
        
        当前用户输入：\(input.userMessage)
        
        请分析这个用户输入的意图。
        """
    }
    
    /// 解析意图分析结果
    private func parseIntentAnalysis(_ response: String) -> IntentAnalysisResult {
        let lines = response.components(separatedBy: .newlines)
        
        var intentType = "simple_chat"
        var confidence = 0.5
        var reasoning = "默认分析"
        var suggestedAgents: [String] = []
        
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            if trimmed.hasPrefix("意图类型：") {
                intentType = String(trimmed.dropFirst("意图类型：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
            } else if trimmed.hasPrefix("置信度：") {
                let confidenceStr = String(trimmed.dropFirst("置信度：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
                confidence = Double(confidenceStr) ?? 0.5
            } else if trimmed.hasPrefix("分析理由：") {
                reasoning = String(trimmed.dropFirst("分析理由：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
            } else if trimmed.hasPrefix("建议智能体：") {
                let agentsStr = String(trimmed.dropFirst("建议智能体：".count)).trimmingCharacters(in: .whitespacesAndNewlines)
                suggestedAgents = agentsStr.components(separatedBy: "、").map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            }
        }
        
        print("🎯 意图分析结果: \(intentType) (置信度: \(confidence))")
        print("📝 分析理由: \(reasoning)")
        
        return IntentAnalysisResult(
            intentType: intentType,
            confidence: confidence,
            reasoning: reasoning,
            suggestedAgents: suggestedAgents
        )
    }
    
    // MARK: - 高级意图分析方法
    
    /// 基于历史对话进行上下文意图分析
    func analyzeIntentWithHistory(_ input: AgentInput) async -> ProcessingResult {
        print("📚 基于历史对话进行深度意图分析...")
        
        // 获取相关的历史记忆
        let relevantMemories = await sharedState.getRelevantMemories(for: input.userMessage, limit: 5)
        
        // 构建包含历史记忆的系统提示词
        let systemPrompt = generateHistoryAwarePrompt(memories: relevantMemories)
        
        // 准备用户消息
        let userMessage = """
        历史对话背景：
        \(relevantMemories.map { "\($0.ragText)" }.joined(separator: "\n"))
        
        当前用户输入：\(input.userMessage)
        
        请基于历史对话背景分析当前用户输入的深层意图。
        """
        
        // 调用API
        let apiResponse = await callAPI(systemPrompt: systemPrompt, userMessage: userMessage)
        
        // 解析结果
        let intentAnalysis = parseIntentAnalysis(apiResponse)
        
        return ProcessingResult(
            content: intentAnalysis.intentType,
            confidence: intentAnalysis.confidence,
            metadata: [
                "analysis_type": "history_aware",
                "detected_intent": intentAnalysis.intentType,
                "confidence": intentAnalysis.confidence,
                "reasoning": intentAnalysis.reasoning,
                "used_memories": relevantMemories.count
            ]
        )
    }
    
    /// 生成历史感知的系统提示词
    private func generateHistoryAwarePrompt(memories: [ChatHistory]) -> String {
        return """
        你是一个高级的用户意图分析助手，能够基于历史对话背景进行深度意图分析。
        
        你需要考虑：
        1. 用户的历史对话模式和偏好
        2. 当前对话与历史对话的关联性
        3. 用户可能的潜在需求和情感状态
        4. 对话的连续性和发展趋势
        
        请返回详细的意图分析结果，格式同之前的要求。
        
        特别注意：
        - 如果用户提到了之前讨论过的话题，优先考虑deep_chat或emotional_support
        - 如果用户表现出情感变化，重点关注emotional_support需求
        - 如果用户询问具体的旅行相关问题，优先考虑travel_planning
        """
    }

    // MARK: - 新增辅助方法

    /// 准备包含时间和上下文信息的用户消息
    private func prepareUserMessageWithTimeContext(_ input: AgentInput) -> String {
        let currentTime = getCurrentTimeString()
        let dayOfWeek = getCurrentDayOfWeek()

        var message = """
        当前时间：\(currentTime)
        星期：\(dayOfWeek)
        用户输入：\(input.userMessage)
        消息类型：\(input.messageType.rawValue)
        """

        // 添加对话历史上下文
        let recentMessages = input.context.conversationHistory.suffix(3)
        if !recentMessages.isEmpty {
            message += "\n\n最近对话："
            for turn in recentMessages {
                message += "\n用户：\(turn.userMessage)"
                if let aiResp = turn.aiResponse {
                    message += "\n小旅：\(aiResp)"
                }
            }
        }

        return message
    }

    /// 获取当前时间字符串
    private func getCurrentTimeString() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: Date())
    }

    /// 获取当前星期
    private func getCurrentDayOfWeek() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: Date())
    }

    /// 解析任务决策JSON
    private func parseTaskDecisionJSON(_ jsonString: String) -> TaskDecision? {
        // 清理JSON字符串，移除可能的markdown标记
        let cleanedJSON = jsonString
            .replacingOccurrences(of: "```json", with: "")
            .replacingOccurrences(of: "```", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        guard let jsonData = cleanedJSON.data(using: .utf8) else {
            print("❌ JSON字符串转换失败")
            return nil
        }

        do {
            let decision = try JSONDecoder().decode(TaskDecision.self, from: jsonData)
            print("✅ JSON解析成功")
            return decision
        } catch {
            print("❌ JSON解析失败: \(error)")
            // 尝试从文本中提取关键信息
            return extractTaskDecisionFromText(cleanedJSON)
        }
    }

    /// 从文本中提取任务决策信息（备用方案）
    private func extractTaskDecisionFromText(_ text: String) -> TaskDecision? {
        print("🔄 使用文本解析备用方案")

        return TaskDecision(
            chatText: "用户问题需要处理",
            confidence: 0.6,
            reasoning: "文本解析备用方案"
        )
    }
}

// MARK: - 意图分析结果模型

/// 任务决策结果（JSON格式）
struct TaskDecision: Codable {
    let chatText: String                     // 传给聊天智能体的文本
    let confidence: Double                   // 决策置信度
    let reasoning: String                    // 分析理由

    enum CodingKeys: String, CodingKey {
        case chatText = "chat_text"
        case confidence
        case reasoning
    }
}

/// 意图分析结果（保留兼容性）
struct IntentAnalysisResult {
    let intentType: String          // 意图类型
    let confidence: Double          // 置信度
    let reasoning: String           // 分析理由
    let suggestedAgents: [String]   // 建议的智能体
}
