import Foundation

/// 文本分割工具类
/// 提供统一的文本分割逻辑，确保TTS播放和UI显示使用相同的分割方式
class TextSegmentationUtils {
    
    /// 情感片段结构
    struct EmotionSegment {
        let text: String
        let emotion: String?
        let order: Int
    }
    
    /// 按情感标签分割文本（与StreamingTTSManager保持一致）
    /// - Parameters:
    ///   - text: 要分割的文本
    ///   - removeEmotionTags: 是否移除情感标签（用于UI显示）
    /// - Returns: 分割后的片段数组
    static func splitTextByEmotionTags(_ text: String, removeEmotionTags: Bool = false) -> [EmotionSegment] {
        print("🎭 开始按情感标签分割文本...")
        
        var segments: [EmotionSegment] = []
        var remainingText = text
        var currentOrder = 0
        
        // 正则表达式匹配 </情感/> 格式的标签
        let pattern = #"(.*?)</([^/>]+)/>"#
        
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: [.dotMatchesLineSeparators])
            
            while !remainingText.isEmpty {
                let range = NSRange(location: 0, length: remainingText.utf16.count)
                
                if let match = regex.firstMatch(in: remainingText, options: [], range: range) {
                    // 提取文本内容和情感标签
                    let textRange = Range(match.range(at: 1), in: remainingText)!
                    let emotionRange = Range(match.range(at: 2), in: remainingText)!
                    
                    let segmentText = String(remainingText[textRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                    let emotionString = String(remainingText[emotionRange])
                    
                    if !segmentText.isEmpty {
                        let finalText = removeEmotionTags ? segmentText : "\(segmentText)</\(emotionString)/>"
                        segments.append(EmotionSegment(
                            text: finalText,
                            emotion: emotionString,
                            order: currentOrder
                        ))
                        currentOrder += 1
                        print("🎭 提取片段 \(currentOrder): \"\(segmentText.prefix(30))...\" -> \(emotionString)")
                    }
                    
                    // 移除已处理的部分
                    let fullMatchRange = Range(match.range, in: remainingText)!
                    remainingText = String(remainingText[fullMatchRange.upperBound...])
                } else {
                    // 没有更多标签，处理剩余文本
                    let finalText = remainingText.trimmingCharacters(in: .whitespacesAndNewlines)
                    if !finalText.isEmpty {
                        segments.append(EmotionSegment(
                            text: finalText,
                            emotion: nil,
                            order: currentOrder
                        ))
                        print("🎭 最终片段: \"\(finalText.prefix(30))...\" -> 无情感标签")
                    }
                    break
                }
            }
        } catch {
            print("❌ 情感标签解析失败: \(error)")
            // 解析失败时返回原始文本
            segments.append(EmotionSegment(text: text, emotion: nil, order: 0))
        }
        
        print("🎭 情感分割完成，共 \(segments.count) 个片段")
        return segments
    }
    
    /// 获取用于UI显示的文本片段（移除情感标签）
    /// - Parameter text: 带有情感标签的文本
    /// - Returns: 用于UI显示的纯文本片段数组
    static func getDisplaySegments(from text: String) -> [String] {
        let segments = splitTextByEmotionTags(text, removeEmotionTags: true)
        return segments.map { $0.text }
    }
    
    /// 获取用于TTS播放的文本片段（保留情感标签）
    /// - Parameter text: 带有情感标签的文本
    /// - Returns: 用于TTS播放的带标签文本片段数组
    static func getTTSSegments(from text: String) -> [String] {
        let segments = splitTextByEmotionTags(text, removeEmotionTags: false)
        return segments.map { $0.text }
    }
    
    /// 传统的标点符号分句方法（作为备用）
    /// - Parameter text: 要分割的文本
    /// - Returns: 按标点符号分割的句子数组
    static func splitBySentencePunctuation(_ text: String) -> [String] {
        let sentences = text.components(separatedBy: CharacterSet(charactersIn: "。！？.!?"))
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines) }
            .filter { !$0.isEmpty }
        
        return sentences.isEmpty ? [text] : sentences
    }
    
    /// 智能分句方法：优先使用情感标签分割，如果没有标签则使用标点符号分割
    /// - Parameters:
    ///   - text: 要分割的文本
    ///   - forDisplay: 是否用于显示（true=移除情感标签，false=保留情感标签）
    /// - Returns: 分割后的文本片段数组
    static func smartSplit(_ text: String, forDisplay: Bool = true) -> [String] {
        // 检查是否包含情感标签
        if text.contains("</") && text.contains("/>") {
            // 使用情感标签分割
            return forDisplay ? getDisplaySegments(from: text) : getTTSSegments(from: text)
        } else {
            // 使用传统标点符号分割
            return splitBySentencePunctuation(text)
        }
    }
}
