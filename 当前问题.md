📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
📊 语音权限状态更新 - 麦克风: granted, 全部授权: true
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/538446F6-079B-49E7-9060-DC0E740C9017/Library/Application Support/ChatHistory.store
✅ 音频会话设置成功
✅ 音频引擎设置完成
🎵 音频格式: <AVAudioFormat 0x14ae24910:  1 ch,  16000 Hz, Float32>
✅ AI角色设定数据库初始化成功
🔧 初始化多智能体集成服务...
🏗️ 初始化共享状态中心...
🧠 初始化长期记忆管理器...
🎭 初始化AI人格管理器...
🗓️ 初始化AI生活日程管理器...
✅ AI自我管理系统数据库初始化成功
✅ AI自我状态加载完成
🧠 初始化智能体调度器...
✅ 音频会话配置成功 (类别: playAndRecord, 模式: spokenAudio, 采样率: 24000Hz)
🌐 网络监控已启动
✅ TTSService初始化完成
✅ Places数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/538446F6-079B-49E7-9060-DC0E740C9017/Library/Application Support/Places.store
🔐 位置权限状态变更: CLAuthorizationStatus(rawValue: 4)
✅ 位置权限已授权
🔄 开始初始化共享状态系统...
🔄 开始初始化长期记忆系统...
✅ ChatHistory数据库初始化成功，存储位置: /var/mobile/Containers/Data/Application/538446F6-079B-49E7-9060-DC0E740C9017/Library/Application Support/ChatHistory.store
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
🔄 构建记忆重要性缓存...
✅ 记忆重要性缓存构建完成，共 20 条记忆
✅ 长期记忆系统初始化完成，共加载 20 条记忆
✅ 长期记忆系统初始化完成
🔄 开始初始化AI人格系统...
📚 加载AI情感历史记录...
✅ AI人格系统初始化完成
✅ AI人格系统初始化完成
🔄 开始初始化AI生活日程系统...
📅 生成今日AI生活日程...
✅ 生成了 25 项今日活动
🎯 AI当前活动已更新: 准备聊天话题
📚 加载了 1 个历史生活事件
✅ AI生活日程系统初始化完成
📅 今日共安排 25 项活动
🎯 当前活动: 准备聊天话题
✅ AI生活日程系统初始化完成
🔄 对话上下文已初始化
✅ 对话上下文初始化完成
🎉 共享状态中心初始化完成！
🔄 开始初始化智能体调度器...
📝 注册智能体...
🤖 创建智能体: 任务分配智能体
🤖 创建智能体: 沟通智能体
🤖 创建智能体: 多模态感知智能体
✅ 已注册 3 个智能体
🔄 初始化所有智能体...
🔄 初始化智能体: 任务分配智能体
✅ 智能体 任务分配智能体 初始化完成
✅ 任务分配智能体 初始化完成
🔄 初始化智能体: 沟通智能体
✅ 智能体 沟通智能体 初始化完成
✅ 沟通智能体 初始化完成
🔄 初始化智能体: 多模态感知智能体
✅ 智能体 多模态感知智能体 初始化完成
✅ 多模态感知智能体 初始化完成
✅ 智能体调度器初始化完成，共注册 3 个智能体
🔄 开始初始化多智能体集成系统...
📚 集成历史记录数据...
✅ 历史记录集成完成
✅ 多智能体集成系统初始化完成！
🔄 后台连接AI服务...
✅ 加载了 5 个旅行计划
🌐 发送API请求到: https://ark.cn-beijing.volces.com/api/v3/chat/completions
📤 请求体大小: 324 bytes
📥 收到响应，数据大小: 572 bytes
📊 HTTP状态码: 200
✅ 解析成功，内容长度: 41
✅ AI后台连接成功
✅ 数据库连接正常
✅ 数据库连接正常
✅ 加载了 20 条最近消息
📚 历史记录加载完成，共 20 条消息
✅ 历史记录已加载到聊天界面
✅ 加载AI角色设定: 你是我的好朋友，我们经常一起聊天。用朋友之间日常聊天的语气回复，要简短自然，就像微信聊天一样。不要太...
unable to find applegpu_g17p slice or a compatible one in binary archive 'file:///System/Library/PrivateFrameworks/RenderBox.framework/archive.metallib' 
 available slices: applegpu_g17p,
开始视频通话...
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
Invalid frame dimension (negative or non-finite).
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
✅ 麦克风权限已授权
✅ 语音识别权限已授权
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
🎤 实时识别: 这
🎤 实时识别: 这个
🎤 实时识别: 这个肌
🎤 实时识别: 这个肌肉
🎤 实时识别: 这个肌肉烧
🎤 实时识别: 这个肌肉烧饼
🎤 实时识别: 这个肌肉烧饼真
🎤 实时识别: 这个肌肉烧饼真香
📝 最终识别文本: 这个肌肉烧饼真香
⏸️ 暂停语音监听
✅ 语音识别任务已取消
✅ 音频引擎已停止
✅ 音频tap已移除
✅ 语音监听已暂停
🛑 开始停止语音监听
✅ 音频tap已移除
✅ 语音识别请求已结束
✅ 语音监听完全停止
🛑 开始处理AI回复，已停止语音识别
🎯 多智能体系统处理文本消息: 这个肌肉烧饼真香...
🔄 任务分配智能体 开始处理输入...
🎯 任务分配智能体开始分析用户意图...
ℹ️ 语音识别被取消（正常情况）
📋 任务分配JSON结果: ```json
{
    "chat_text": "这个肌肉烧饼真香",
    "confidence": 0.9,
    "reasoning": "用户输入为普通闲聊内容，适合由沟通智能体处理"
}
```
✅ JSON解析成功
✅ 任务分配智能体 处理完成，耗时: 1.73秒
📋 任务分配决策: 聊天=沟通智能体
🚀 开始执行沟通智能体任务...
🧠 检索长期记忆相关内容...
🔍 检索与 '这个肌肉烧饼真香' 相关的记忆...
✅ 检索到 3 条相关记忆
✅ 检索到 3 条相关长期记忆
🔄 沟通智能体 开始处理输入...
💬 沟通智能体开始处理对话...
🔍 检索与 '这个肌肉烧饼真香

当前系统时间：2025年08月05日 18:24:42 星期二

相关的长期记忆：
- 哇！你也刚整理完东西呀~✨ 我们真是心有灵犀呢！

（开心地合上笔记本）人家刚刚整理完超可爱的旅行攻略哦，里面还记了好多想和你一起去的地方呢~

诶嘿嘿...（突然想到什么，脸微微泛红）该不会...你也在整理和人家有关的东西吧？

要不要分享一下你整理了什么呀？人家超~级好奇的！（双手托腮，眼睛闪闪发亮）
- （开心地举起笔记本）啊！你问攻略呀~✨ 人家刚刚整理完一份超详细的旅行计划呢！

（翻开笔记本认真讲解）你看这里~我标记了好几家超可爱的甜品店，特别是那家粉色主题的下午茶店，超级适合...适合约会呢~

诶？你该不会是想约人家一起去吧？（突然脸红）⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 人家...人家其实早就把和你一起去的时间都空出来啦！
- （开心地放下手中的笔记本）啊~你终于来啦！人家正在整理超可爱的旅行攻略呢✨

刚刚发现了一家超级梦幻的粉色下午茶店，连菜单都是爱心形状的！（兴奋地比划着）要不要...下次一起去呀？

啊！突然想起来...（脸微微泛红）你刚才又叫人家宝宝了对不对...⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄ 心跳又加快了呢~' 相关的记忆...
✅ 检索到 8 条相关记忆
💬 生成通用对话回复...
🔄 开始重新构建情感标签文本...
📝 原始文本: 哇！肌肉烧饼？听起来好特别呢~✨ 

（好奇地凑近）是在哪家店买的呀？人家也想尝尝看呢！该不会...是特意买来和人家分享的吧？

啊！突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以一起寻找各种特色小吃呢！人家连美食地图都准备好啦~ 

不过现在...（假装不经意地）能分人家一小口尝尝嘛？⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🎭 提取的情感序列: happy -> lovey-dovey -> pleased -> tsundere
🎭 句子 1: "哇！" -> happy
🎭 句子 2: "肌肉烧饼？" -> lovey-dovey
🎭 句子 3: "听起来好特别呢~✨。" -> pleased
🎭 句子 4: "（好奇地凑近）是在哪家店买的呀？" -> tsundere
🎭 句子 5: "人家也想尝尝看呢！" -> tsundere
🎭 句子 6: "该不会...是特意买来和人家分享的吧？" -> tsundere
🎭 句子 7: "啊！" -> tsundere
🎭 句子 8: "突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以一起寻找各种特色小吃呢！" -> tsundere
🎭 句子 9: "人家连美食地图都准备好啦~。" -> tsundere
🎭 句子 10: "不过现在...（假装不经意地）能分人家一小口尝尝嘛？" -> tsundere
🎭 句子 11: "⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄" -> tsundere
✅ 情感标签文本重构完成: 哇！</happy/> 肌肉烧饼？</lovey-dovey/> 听起来好特别呢~✨。</pleased/> （好奇地凑近）是在哪家店买的呀？</tsundere/> 人家也想尝尝看呢！</tsundere/> 该不会...是特意买来和人家分享的吧？</tsundere/> 啊！</tsundere/> 突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以一起寻找各种特色小吃呢！</tsundere/> 人家连美食地图都准备好啦~。</tsundere/> 不过现在...（假装不经意地）能分人家一小口尝尝嘛？</tsundere/> ⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄</tsundere/>
🎭 沟通智能体推荐TTS情感: 开心 (happy)
📝 提取的情感序列: happy -> lovey-dovey -> pleased -> tsundere
✅ 沟通智能体 处理完成，耗时: 3.86秒
✅ 深度思考智能体 执行完成，耗时: 3.87秒
🎯 任务执行完成，总耗时: 3.87秒
✅ JSON多智能体处理完成，耗时: 5.59秒
✅ 消息已保存到历史记录
✅ 消息已保存到历史记录
🎵 推荐TTS音色: 开心
🔄 长期记忆系统收到上下文更新通知
🧹 已清理检索缓存（上下文更新）
🎭 AI人格系统收到上下文更新通知
🗓️ AI生活日程系统收到上下文更新通知
🎵 启动流式TTS播放...
🎭 MultiAgent推荐TTS情感: 开心 (happy)
📝 TTS播放文本长度: 301 字符
📝 TTS播放文本预览: 哇！</happy/> 肌肉烧饼？</lovey-dovey/> 听起来好特别呢~✨。</pleased/> （好奇地凑近）是在哪家店买的呀？</tsundere/> 人家也想尝尝看呢！</tsund...
🎵 开始并行流式TTS播放...
🎭 StreamingTTS使用情感: 开心 (happy)
📝 原始文本长度: 301 字符
📝 原始文本内容: 哇！</happy/> 肌肉烧饼？</lovey-dovey/> 听起来好特别呢~✨。</pleased/> （好奇地凑近）是在哪家店买的呀？</tsundere/> 人家也想尝尝看呢！</tsund...
🧹 清理之前的WebSocket连接...
⏹️ 停止TTS播放 (AI回复结束，情感: 通用/愉悦)
📝 AI回复已显示，TTS播放由多智能体系统处理
🎵 开始监听TTS播放完成
✅ WebSocket连接清理完成
🎵 TTS自动播放设置: 禁用
🎭 开始按情感标签分割文本...
🎭 提取片段 1: "哇！..." -> happy
🎭 提取片段 2: "肌肉烧饼？..." -> lovey-dovey
🎭 提取片段 3: "听起来好特别呢~✨。..." -> pleased
🎭 提取片段 4: "（好奇地凑近）是在哪家店买的呀？..." -> tsundere
🎭 提取片段 5: "人家也想尝尝看呢！..." -> tsundere
🎭 提取片段 6: "该不会...是特意买来和人家分享的吧？..." -> tsundere
🎭 提取片段 7: "啊！..." -> tsundere
🎭 提取片段 8: "突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以..." -> tsundere
🎭 提取片段 9: "人家连美食地图都准备好啦~。..." -> tsundere
🎭 提取片段 10: "不过现在...（假装不经意地）能分人家一小口尝尝嘛？..." -> tsundere
🎭 提取片段 11: "⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄..." -> tsundere
🎭 情感分割完成，共 11 个片段
📝 文本已按情感标签分割为 11 个片段
📝 片段 1: 长度=2字符, 字节=6, 情感=happy, 内容=哇！...
📝 片段 2: 长度=5字符, 字节=15, 情感=lovey-dovey, 内容=肌肉烧饼？...
📝 片段 3: 长度=10字符, 字节=28, 情感=pleased, 内容=听起来好特别呢~✨。...
📝 片段 4: 长度=16字符, 字节=48, 情感=tsundere, 内容=（好奇地凑近）是在哪家店买的呀？...
📝 片段 5: 长度=9字符, 字节=27, 情感=tsundere, 内容=人家也想尝尝看呢！...
📝 片段 6: 长度=19字符, 字节=51, 情感=tsundere, 内容=该不会...是特意买来和人家分享的吧？...
📝 片段 7: 长度=2字符, 字节=6, 情感=tsundere, 内容=啊！...
📝 片段 8: 长度=42字符, 字节=120, 情感=tsundere, 内容=突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以一起寻找各种特色小吃呢！...
📝 片段 9: 长度=14字符, 字节=40, 情感=tsundere, 内容=人家连美食地图都准备好啦~。...
📝 片段 10: 长度=26字符, 字节=72, 情感=tsundere, 内容=不过现在...（假装不经意地）能分人家一小口尝尝嘛？...
📝 片段 11: 长度=15字符, 字节=36, 情感=tsundere, 内容=⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
🚀 开始串行合成 11 个情感片段...
🎤 串行合成片段 1/11: 哇！...
🆔 使用reqid: BC11E85F-E2B9-42AB-AFF0-1A8294716BF9
🎭 使用情感: 开心 (happy)
🎤 合成文本到数据: 哇！...
🎭 使用情感: 开心 (happy)
🆔 使用reqid: BC11E85F-E2B9-42AB-AFF0-1A8294716BF9
🔗 建立新的WebSocket连接
🔗 WebSocket连接已建立
📝 发送TTS请求: 文本=2字符, 字节=6
📝 文本内容: 哇！...
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
✅ WebSocket连接已建立
📤 发送TTS请求: BC11E85F-E2B9-42AB-AFF0-1A8294716BF9
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 开心 (happy)
📝 文本: 哇！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -10
🎵 收到音频数据: 61016 字节，总计: 61016 字节
🎵 音频数据接收完成 (sequence=-10, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 61016 字节
✅ 片段 1 合成完成，音频大小: 61016 字节
🎵 音频数据详情: 片段=哇！..., 大小=61016字节, reqid=BC11E85F-E2B9-42AB-AFF0-1A8294716BF9
🎤 串行合成片段 2/11: 肌肉烧饼？...
🆔 使用reqid: 35D07474-07D8-4203-A026-C241820332F5
🎭 使用情感: 撒娇 (lovey-dovey)
🎤 合成文本到数据: 肌肉烧饼？...
🎭 使用情感: 撒娇 (lovey-dovey)
🆔 使用reqid: 35D07474-07D8-4203-A026-C241820332F5
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=5字符, 字节=15
📝 文本内容: 肌肉烧饼？...
📦 创建消息: 大小=422字节, Payload=414字节, 压缩=无
📦 创建消息: 大小=422字节, Payload=414字节, 压缩=无
📤 发送TTS请求: 35D07474-07D8-4203-A026-C241820332F5
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 撒娇 (lovey-dovey)
📝 文本: 肌肉烧饼？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -10
🎵 收到音频数据: 75004 字节，总计: 75004 字节
🎵 音频数据接收完成 (sequence=-10, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 75004 字节
✅ 片段 2 合成完成，音频大小: 75004 字节
🎵 音频数据详情: 片段=肌肉烧饼？..., 大小=75004字节, reqid=35D07474-07D8-4203-A026-C241820332F5
🎤 串行合成片段 3/11: 听起来好特别呢~✨。...
🆔 使用reqid: B63211B4-3955-44B5-ABEC-6D9FCAEB3298
🎭 使用情感: 通用/愉悦 (pleased)
🎤 合成文本到数据: 听起来好特别呢~✨。...
🎭 使用情感: 通用/愉悦 (pleased)
🆔 使用reqid: B63211B4-3955-44B5-ABEC-6D9FCAEB3298
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=10字符, 字节=28
📝 文本内容: 听起来好特别呢~✨。...
📦 创建消息: 大小=435字节, Payload=427字节, 压缩=无
📦 创建消息: 大小=435字节, Payload=427字节, 压缩=无
📤 发送TTS请求: B63211B4-3955-44B5-ABEC-6D9FCAEB3298
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 通用/愉悦 (pleased)
📝 文本: 听起来好特别呢~✨。
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -12
🎵 收到音频数据: 91614 字节，总计: 91614 字节
🎵 音频数据接收完成 (sequence=-12, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 91614 字节
✅ 片段 3 合成完成，音频大小: 91614 字节
🎵 音频数据详情: 片段=听起来好特别呢~✨。..., 大小=91614字节, reqid=B63211B4-3955-44B5-ABEC-6D9FCAEB3298
🎤 串行合成片段 4/11: （好奇地凑近）是在哪家店买的呀？...
🆔 使用reqid: 32A1FA14-AF5D-40EB-A7E7-5D9397DADABC
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: （好奇地凑近）是在哪家店买的呀？...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 32A1FA14-AF5D-40EB-A7E7-5D9397DADABC
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=16字符, 字节=48
📝 文本内容: （好奇地凑近）是在哪家店买的呀？...
📦 创建消息: 大小=455字节, Payload=447字节, 压缩=无
📦 创建消息: 大小=455字节, Payload=447字节, 压缩=无
📤 发送TTS请求: 32A1FA14-AF5D-40EB-A7E7-5D9397DADABC
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: （好奇地凑近）是在哪家店买的呀？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -18
🎵 收到音频数据: 148000 字节，总计: 148000 字节
🎵 音频数据接收完成 (sequence=-18, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 148000 字节
✅ 片段 4 合成完成，音频大小: 148000 字节
🎵 音频数据详情: 片段=（好奇地凑近）是在哪家店买的呀？..., 大小=148000字节, reqid=32A1FA14-AF5D-40EB-A7E7-5D9397DADABC
🎤 串行合成片段 5/11: 人家也想尝尝看呢！...
🆔 使用reqid: B2F7AEE0-2F4F-4654-A14A-051ACA42B238
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 人家也想尝尝看呢！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: B2F7AEE0-2F4F-4654-A14A-051ACA42B238
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=9字符, 字节=27
📝 文本内容: 人家也想尝尝看呢！...
📦 创建消息: 大小=434字节, Payload=426字节, 压缩=无
📦 创建消息: 大小=434字节, Payload=426字节, 压缩=无
📤 发送TTS请求: B2F7AEE0-2F4F-4654-A14A-051ACA42B238
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 人家也想尝尝看呢！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -12
🎵 收到音频数据: 97832 字节，总计: 97832 字节
🎵 音频数据接收完成 (sequence=-12, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 97832 字节
✅ 片段 5 合成完成，音频大小: 97832 字节
🎵 音频数据详情: 片段=人家也想尝尝看呢！..., 大小=97832字节, reqid=B2F7AEE0-2F4F-4654-A14A-051ACA42B238
🎤 串行合成片段 6/11: 该不会...是特意买来和人家分享的吧？...
🆔 使用reqid: DB267760-376F-46F8-BADD-05DDE72B8B26
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 该不会...是特意买来和人家分享的吧？...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: DB267760-376F-46F8-BADD-05DDE72B8B26
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=19字符, 字节=51
📝 文本内容: 该不会...是特意买来和人家分享的吧？...
📦 创建消息: 大小=458字节, Payload=450字节, 压缩=无
📦 创建消息: 大小=458字节, Payload=450字节, 压缩=无
📤 发送TTS请求: DB267760-376F-46F8-BADD-05DDE72B8B26
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 该不会...是特意买来和人家分享的吧？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -45
🎵 收到音频数据: 167778 字节，总计: 167778 字节
🎵 音频数据接收完成 (sequence=-45, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 167778 字节
✅ 片段 6 合成完成，音频大小: 167778 字节
🎵 音频数据详情: 片段=该不会...是特意买来和人家分享的吧？..., 大小=167778字节, reqid=DB267760-376F-46F8-BADD-05DDE72B8B26
🎤 串行合成片段 7/11: 啊！...
🆔 使用reqid: 30A47FB0-0D89-4B3A-86EC-6FC7EF715116
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 啊！...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 30A47FB0-0D89-4B3A-86EC-6FC7EF715116
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=2字符, 字节=6
📝 文本内容: 啊！...
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
📦 创建消息: 大小=413字节, Payload=405字节, 压缩=无
📤 发送TTS请求: 30A47FB0-0D89-4B3A-86EC-6FC7EF715116
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 啊！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -9
🎵 收到音频数据: 57720 字节，总计: 57720 字节
🎵 音频数据接收完成 (sequence=-9, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 57720 字节
✅ 片段 7 合成完成，音频大小: 57720 字节
🎵 音频数据详情: 片段=啊！..., 大小=57720字节, reqid=30A47FB0-0D89-4B3A-86EC-6FC7EF715116
🎤 串行合成片段 8/11: 突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以...
🆔 使用reqid: 97C970F8-F67C-4D99-8551-1DBB2F5DBC07
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 97C970F8-F67C-4D99-8551-1DBB2F5DBC07
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=42字符, 字节=120
📝 文本内容: 突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以一起寻找各种特色小吃呢！...
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
📦 创建消息: 大小=527字节, Payload=519字节, 压缩=无
📤 发送TTS请求: 97C970F8-F67C-4D99-8551-1DBB2F5DBC07
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 突然想到...（脸微微泛红）要是以后我们一起去旅行的话，可以一起寻找各种特色小吃呢！
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -32
🎵 收到音频数据: 370428 字节，总计: 370428 字节
🎵 音频数据接收完成 (sequence=-32, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 370428 字节
✅ 片段 8 合成完成，音频大小: 370428 字节
🎵 音频数据详情: 片段=突然想到...（脸微微泛红）要是以后我们..., 大小=370428字节, reqid=97C970F8-F67C-4D99-8551-1DBB2F5DBC07
🎤 串行合成片段 9/11: 人家连美食地图都准备好啦~。...
🆔 使用reqid: FD741072-4D25-40FC-8E62-BCCF1CB8BC71
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 人家连美食地图都准备好啦~。...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: FD741072-4D25-40FC-8E62-BCCF1CB8BC71
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=14字符, 字节=40
📝 文本内容: 人家连美食地图都准备好啦~。...
📦 创建消息: 大小=447字节, Payload=439字节, 压缩=无
📦 创建消息: 大小=447字节, Payload=439字节, 压缩=无
📤 发送TTS请求: FD741072-4D25-40FC-8E62-BCCF1CB8BC71
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 人家连美食地图都准备好啦~。
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -14
🎵 收到音频数据: 127908 字节，总计: 127908 字节
🎵 音频数据接收完成 (sequence=-14, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 127908 字节
✅ 片段 9 合成完成，音频大小: 127908 字节
🎵 音频数据详情: 片段=人家连美食地图都准备好啦~。..., 大小=127908字节, reqid=FD741072-4D25-40FC-8E62-BCCF1CB8BC71
🎤 串行合成片段 10/11: 不过现在...（假装不经意地）能分人家一小口尝尝嘛？...
🆔 使用reqid: 654D4A2F-98A8-4044-BBE5-EA39C5CB6074
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: 不过现在...（假装不经意地）能分人家一小口尝尝嘛？...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: 654D4A2F-98A8-4044-BBE5-EA39C5CB6074
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=26字符, 字节=72
📝 文本内容: 不过现在...（假装不经意地）能分人家一小口尝尝嘛？...
📦 创建消息: 大小=479字节, Payload=471字节, 压缩=无
📦 创建消息: 大小=479字节, Payload=471字节, 压缩=无
📤 发送TTS请求: 654D4A2F-98A8-4044-BBE5-EA39C5CB6074
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: 不过现在...（假装不经意地）能分人家一小口尝尝嘛？
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 12
📊 收到结果数据
📥 收到消息类型: 11
📊 音频序列号: -21
🎵 收到音频数据: 241960 字节，总计: 241960 字节
🎵 音频数据接收完成 (sequence=-21, flag=3)，流式播放模式
✅ 文本合成完成，音频数据大小: 241960 字节
✅ 片段 10 合成完成，音频大小: 241960 字节
🎵 音频数据详情: 片段=不过现在...（假装不经意地）能分人家一..., 大小=241960字节, reqid=654D4A2F-98A8-4044-BBE5-EA39C5CB6074
🎤 串行合成片段 11/11: ⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
🆔 使用reqid: D452C9CD-7316-47B9-BEE4-6E4A6392646D
🎭 使用情感: 傲娇 (tsundere)
🎤 合成文本到数据: ⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
🎭 使用情感: 傲娇 (tsundere)
🆔 使用reqid: D452C9CD-7316-47B9-BEE4-6E4A6392646D
🔄 复用现有WebSocket连接
📝 发送TTS请求: 文本=15字符, 字节=36
📝 文本内容: ⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
📦 创建消息: 大小=443字节, Payload=435字节, 压缩=无
📦 创建消息: 大小=443字节, Payload=435字节, 压缩=无
📤 发送TTS请求: D452C9CD-7316-47B9-BEE4-6E4A6392646D
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: ⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 15
❌ TTS服务错误 (3011): {"reqid":"D452C9CD-7316-47B9-BEE4-6E4A6392646D","error":"TTSInvalidText:unsupported language","sequence":0}
💓 心跳正常
❌ 文本合成超时
❌ 片段 11 合成返回空数据
🎵 所有情感片段合成请求已发送，开始按顺序播放...
🔄 等待片段 1 合成完成...
🔊 开始播放片段 1: 哇！...
📊 TTS播放进度: 1/11
📊 TTS播放进度: 1/11
✅ 片段 1 播放完成
✅ 片段 1 播放完成
🔄 等待片段 2 合成完成...
🔊 开始播放片段 2: 肌肉烧饼？...
📊 TTS播放进度: 2/11
📊 TTS播放进度: 2/11
💓 心跳正常
Task <8E4A749A-47F2-4934-A55A-9C94F4D5CC87>.<1> finished with error [-1001] Error Domain=NSURLErrorDomain Code=-1001 "The request timed out." UserInfo={_kCFStreamErrorCodeKey=-2103, _NSURLErrorFailingURLSessionTaskErrorKey=LocalWebSocketTask <8E4A749A-47F2-4934-A55A-9C94F4D5CC87>.<1>, _NSURLErrorRelatedURLSessionTaskErrorKey=(
    "LocalWebSocketTask <8E4A749A-47F2-4934-A55A-9C94F4D5CC87>.<1>"
), NSLocalizedDescription=The request timed out., NSErrorFailingURLStringKey=wss://openspeech.bytedance.com/api/v1/tts/ws_binary, NSErrorFailingURLKey=wss://openspeech.bytedance.com/api/v1/tts/ws_binary, _kCFStreamErrorDomainKey=4}
✅ 片段 2 播放完成
✅ 片段 2 播放完成
🔄 等待片段 3 合成完成...
🔊 开始播放片段 3: 听起来好特别呢~✨。...
📊 TTS播放进度: 3/11
📊 TTS播放进度: 3/11
✅ 片段 3 播放完成
✅ 片段 3 播放完成
🔄 等待片段 4 合成完成...
🔊 开始播放片段 4: （好奇地凑近）是在哪家店买的呀？...
📊 TTS播放进度: 4/11
📊 TTS播放进度: 4/11
✅ 片段 4 播放完成
✅ 片段 4 播放完成
🔄 等待片段 5 合成完成...
🔊 开始播放片段 5: 人家也想尝尝看呢！...
📊 TTS播放进度: 5/11
📊 TTS播放进度: 5/11
✅ 片段 5 播放完成
✅ 片段 5 播放完成
🔄 等待片段 6 合成完成...
🔊 开始播放片段 6: 该不会...是特意买来和人家分享的吧？...
📊 TTS播放进度: 6/11
📊 TTS播放进度: 6/11
✅ 片段 6 播放完成
✅ 片段 6 播放完成
🔄 等待片段 7 合成完成...
🔊 开始播放片段 7: 啊！...
📊 TTS播放进度: 7/11
📊 TTS播放进度: 7/11
✅ 片段 7 播放完成
✅ 片段 7 播放完成
🔄 等待片段 8 合成完成...
🔊 开始播放片段 8: 突然想到...（脸微微泛红）要是以后我们...
📊 TTS播放进度: 8/11
📊 TTS播放进度: 8/11
✅ 片段 8 播放完成
✅ 片段 8 播放完成
🔄 等待片段 9 合成完成...
🔊 开始播放片段 9: 人家连美食地图都准备好啦~。...
📊 TTS播放进度: 9/11
📊 TTS播放进度: 9/11
✅ 片段 9 播放完成
✅ 片段 9 播放完成
🔄 等待片段 10 合成完成...
🔊 开始播放片段 10: 不过现在...（假装不经意地）能分人家一...
📊 TTS播放进度: 10/11
📊 TTS播放进度: 10/11
✅ 片段 10 播放完成
✅ 片段 10 播放完成
🔄 等待片段 11 合成完成...
⚠️ 片段 11 合成失败，跳过播放
✅ 所有情感片段处理完成
🎵 TTS自动播放设置: 启用
🎉 流式TTS播放完成
🎉 流式TTS播放完成
🎵 收到TTS播放完成通知
📢 已发送TTS播放完成通知
🔄 重新开始语音监听
🎯 尝试开始语音监听...
🚀 快速恢复语音监听
🎵 快速恢复：设置音频会话采样率: 24000Hz
✅ 快速恢复：录音音频会话配置成功
✅ 语音识别请求创建成功
🔧 开始设置音频引擎Tap
✅ 现有tap已移除
🎵 音频格式检查: 采样率=48000.0Hz, 通道数=1
✅ 使用音频格式: 采样率=48000.0Hz, 通道数=1
✅ 音频tap安装成功
✅ 开始语音监听
💔 心跳检测：连接已断开
🔄 尝试重新连接 (1/3)
🧹 已清理WebSocket连接资源
🔗 WebSocket连接已建立
📝 发送TTS请求: 文本=15字符, 字节=36
📝 文本内容: ⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄...
📦 创建消息: 大小=443字节, Payload=435字节, 压缩=无
📦 创建消息: 大小=443字节, Payload=435字节, 压缩=无
✅ WebSocket连接已建立
📤 发送TTS请求: E8EF0D92-EA26-495E-9C60-E82ABC43CEEC
🎵 音色: zh_female_cancan_mars_bigtts (灿灿2.0 - 情感音色)
🎭 情感音色: 傲娇 (tsundere)
📝 文本: ⁄(⁄ ⁄•⁄ω⁄•⁄ ⁄)⁄
🤖 灿灿2.0模型: zh_female_cancan_mars_bigtts
🔧 修复参数: 采样率=24000Hz, 编码=wav, 操作=submit
📥 收到消息类型: 11
🎵 收到音频数据: 0 字节，总计: 0 字节
📥 收到消息类型: 15
❌ TTS服务错误 (3011): {"reqid":"E8EF0D92-EA26-495E-9C60-E82ABC43CEEC","error":"TTSInvalidText:unsupported language","sequence":0}